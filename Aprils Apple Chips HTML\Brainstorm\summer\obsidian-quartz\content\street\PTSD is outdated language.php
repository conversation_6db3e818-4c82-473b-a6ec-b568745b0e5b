<?php
// Auto-generated blog post
// Source: PTSD is outdated language.md

// Load path helper and configuration with fallback
$pathPrefix = '../';
if (file_exists(__DIR__ . '/' . $pathPrefix . 'path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . 'path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . 'config.php';
} elseif (file_exists(__DIR__ . '/' . $pathPrefix . '../path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . '../path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . '../config.php';
} else {
    die('Could not find path-helper.php');
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'PTSD is Outdated Language';
$meta_description = 'Challenging conventional wisdom about PTSD and mental health through research, advocacy, and lived experience. A collection of writings on mental health reform.';
$meta_keywords = 'mental health, ptsd, psychology, advocacy, research, A. A. Chips, blog, personal stories, advocacy, homelessness, family alienation, rebuilding life';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = 'https://www.banyantreatmentcenter.com/wp-content/uploads/2022/09/ptsd-history.png';
$related_posts = [
    ['title' => 'Alienation', 'url' => PathHelper::getCategoryIndexPath('alienation'), 'excerpt' => 'Browse all Alienation posts'],
    ['title' => 'Climate', 'url' => PathHelper::getCategoryIndexPath('climate'), 'excerpt' => 'Browse all Climate posts'],
    ['title' => 'Humor', 'url' => PathHelper::getCategoryIndexPath('humor'), 'excerpt' => 'Browse all Humor posts'],
    ['title' => 'Inspiration', 'url' => PathHelper::getCategoryIndexPath('inspiration'), 'excerpt' => 'Browse all Inspiration posts'],
    ['title' => 'Journal', 'url' => PathHelper::getCategoryIndexPath('journal'), 'excerpt' => 'Browse all Journal posts'],
    ['title' => 'Judaism', 'url' => PathHelper::getCategoryIndexPath('judaism'), 'excerpt' => 'Browse all Judaism posts'],
    ['title' => 'Kitchen', 'url' => PathHelper::getCategoryIndexPath('kitchen'), 'excerpt' => 'Browse all Kitchen posts'],
    ['title' => 'Ptsd Myth', 'url' => PathHelper::getCategoryIndexPath('ptsd-myth'), 'excerpt' => 'Browse all Ptsd Myth posts'],
    ['title' => 'Street', 'url' => PathHelper::getCategoryIndexPath('street'), 'excerpt' => 'Browse all Street posts'],
    ['title' => 'Writings', 'url' => PathHelper::getCategoryIndexPath('writings'), 'excerpt' => 'Browse all Writings posts'],
];

$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'PTSD is Outdated Language',
  'author' => 'A. A. Chips',
  'date' => '2025-05-20',
  'tags' => 
  array (
    0 => 'mental health',
    1 => 'ptsd',
    2 => 'psychology',
    3 => 'advocacy',
    4 => 'research',
  ),
  'excerpt' => 'Challenging conventional wisdom about PTSD and mental health through research, advocacy, and lived experience. A collection of writings on mental health reform.',
  'categories' => 
  array (
    0 => 'PTSD Mythology',
    1 => 'Street Advocacy',
    2 => 'Writings',
  ),
  'thumbnail' => 'https://www.banyantreatmentcenter.com/wp-content/uploads/2022/09/ptsd-history.png',
  'source_file' => 'content\\street\\PTSD is outdated language.md',
);

// Raw content
$post_content = '<p>The term Post Traumatic Stress Disorder (PTSD), while codified in the DSM V (along with cPTSD), uses outdated language that hinders a complete understanding of the condition. The initial word, "Post," meaning after a traumatic event, limits the scope of the disorder.</p>

<p>Coined after the Vietnam War and initially termed "Shell Shock," PTSD was primarily associated with male military veterans experiencing combat trauma far removed from their current reality. This perspective overlooked the experiences of women and falsely assumed those not in combat were immune. However, this understanding is inaccurate. Many individuals meeting PTSD criteria live with ongoing violence, experience intermittent trauma, or even exhibit symptoms related to anticipated future events (sometimes referred to as \'PreTSD\'). The descriptor "Post" inadequately represents the diverse realities of this condition.</p>

<p>The Vietnam War presented unique circumstances, notably the widespread media coverage that undermined the traditional narrative of a just and righteous war. Soldiers witnessed the moral implications of the conflict, including biological warfare like Agent Orange, leading to a profound moral injury and a collapse of their foundational understanding of right and wrong. Their perception of the American government and military as benevolent entities was shattered, resulting in what could be termed Betrayal Trauma.</p>

<p>This breakdown of fundamental beliefs, beyond just the traumatic event itself, and the subsequent need to relearn how to navigate a world where prior foundations are gone, appears to be the core of PTSD.</p>

<p>This experience extends beyond warfare. While the DSM recognizes PTSD and Complex PTSD (often stemming from interpersonal or social trauma, such as childhood abuse or betrayal by a trusted partner), other significant presentations exist, including Ongoing/Persistent Traumatic Stress Disorder, Intermittent Traumatic Stress Disorder, and Pre-Traumatic Stress Disorder.</p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta"><?php echo implode('<span class="meta-separator"> • </span>', $metadata); ?></div>
    <?php endif; ?>
    
    <?php if (isset($post_data['excerpt'])): ?>
        <div class="post-excerpt">
            <p><em><?php echo htmlspecialchars($post_data['excerpt']); ?></em></p>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>