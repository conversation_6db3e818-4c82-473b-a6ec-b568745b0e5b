<?php
/**
 * Comment Handler - API for comment operations
 * For A. A. Chips' Obsidian-Quartz Comments System
 */

session_start();
require_once __DIR__ . '/database.php';
require_once __DIR__ . '/google-auth.php';

header('Content-Type: application/json');

class CommentHandler {
    private $db;
    private $config;

    public function __construct() {
        $this->db = CommentDatabase::getInstance();
        $this->config = $this->db->getConfig();
    }

    public function handleRequest() {
        $method = $_SERVER['REQUEST_METHOD'];
        $action = $_GET['action'] ?? '';

        try {
            switch ($method) {
                case 'GET':
                    $this->handleGet($action);
                    break;
                case 'POST':
                    $this->handlePost($action);
                    break;
                default:
                    throw new Exception('Method not allowed');
            }
        } catch (Exception $e) {
            $this->sendError($e->getMessage(), 400);
        }
    }

    private function handleGet($action) {
        switch ($action) {
            case 'comments':
                $this->getComments();
                break;
            case 'user':
                $this->getCurrentUser();
                break;
            default:
                throw new Exception('Invalid action');
        }
    }

    private function handlePost($action) {
        switch ($action) {
            case 'comment':
                $this->createComment();
                break;
            case 'vote':
                $this->handleVote();
                break;
            default:
                throw new Exception('Invalid action');
        }
    }

    private function getComments() {
        $postSlug = $_GET['post_slug'] ?? '';
        $page = (int)($_GET['page'] ?? 1);

        if (empty($postSlug)) {
            throw new Exception('Post slug is required');
        }

        $comments = $this->db->getComments($postSlug, $page);
        $totalComments = $this->db->getCommentCount($postSlug);
        
        // Add user vote information if logged in
        $currentUser = GoogleAuth::getCurrentUser();
        if ($currentUser) {
            $this->addUserVoteInfo($comments, $currentUser['id']);
        }

        $this->sendSuccess([
            'comments' => $comments,
            'total_comments' => $totalComments,
            'page' => $page,
            'has_more' => count($comments) === $this->config['comments']['comments_per_page']
        ]);
    }

    private function addUserVoteInfo(&$comments, $userId) {
        foreach ($comments as &$comment) {
            $comment['user_vote'] = $this->db->getUserVote($comment['id'], $userId);
            if (!empty($comment['replies'])) {
                $this->addUserVoteInfo($comment['replies'], $userId);
            }
        }
    }

    private function getCurrentUser() {
        $user = GoogleAuth::getCurrentUser();
        $this->sendSuccess(['user' => $user]);
    }

    private function createComment() {
        // Check if user is logged in
        $currentUser = GoogleAuth::getCurrentUser();
        if (!$currentUser) {
            throw new Exception('Authentication required');
        }

        // Get and validate input
        $input = json_decode(file_get_contents('php://input'), true);
        if (!$input) {
            throw new Exception('Invalid JSON input');
        }

        $postSlug = $input['post_slug'] ?? '';
        $content = trim($input['content'] ?? '');
        $parentId = !empty($input['parent_id']) ? (int)$input['parent_id'] : null;
        $honeypot = $input[$this->config['spam']['honeypot_field']] ?? '';

        // Validate input
        if (empty($postSlug)) {
            throw new Exception('Post slug is required');
        }

        if (empty($content)) {
            throw new Exception('Comment content is required');
        }

        if (strlen($content) < $this->config['comments']['min_comment_length']) {
            throw new Exception('Comment is too short');
        }

        if (strlen($content) > $this->config['comments']['max_comment_length']) {
            throw new Exception('Comment is too long');
        }

        // Honeypot check (bot detection)
        if (!empty($honeypot)) {
            throw new Exception('Spam detected');
        }

        // Rate limiting
        $ipAddress = $this->getClientIp();
        if (!$this->db->checkRateLimit($ipAddress, $currentUser['id'], 'comment')) {
            throw new Exception('Rate limit exceeded. Please wait before posting another comment.');
        }

        // Sanitize content
        if (!$this->config['comments']['allow_html']) {
            $content = htmlspecialchars($content, ENT_QUOTES, 'UTF-8');
        } else {
            $content = strip_tags($content, $this->config['comments']['allowed_html_tags']);
        }

        // Create comment
        $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';
        $commentId = $this->db->createComment(
            $postSlug,
            $currentUser['id'],
            $content,
            $parentId,
            $ipAddress,
            $userAgent
        );

        // Record rate limit
        $this->db->recordRateLimit($ipAddress, $currentUser['id'], 'comment');

        $this->sendSuccess([
            'comment_id' => $commentId,
            'message' => 'Comment posted successfully'
        ]);
    }

    private function handleVote() {
        // Check if user is logged in
        $currentUser = GoogleAuth::getCurrentUser();
        if (!$currentUser) {
            throw new Exception('Authentication required');
        }

        // Get and validate input
        $input = json_decode(file_get_contents('php://input'), true);
        if (!$input) {
            throw new Exception('Invalid JSON input');
        }

        $commentId = (int)($input['comment_id'] ?? 0);
        $voteType = $input['vote_type'] ?? '';

        if ($commentId <= 0) {
            throw new Exception('Invalid comment ID');
        }

        if (!in_array($voteType, ['like', 'dislike', 'remove'])) {
            throw new Exception('Invalid vote type');
        }

        // Rate limiting
        $ipAddress = $this->getClientIp();
        if (!$this->db->checkRateLimit($ipAddress, $currentUser['id'], 'vote')) {
            throw new Exception('Rate limit exceeded. Please wait before voting again.');
        }

        // Handle vote
        if ($voteType === 'remove') {
            $this->db->removeVote($commentId, $currentUser['id']);
        } else {
            $this->db->addVote($commentId, $currentUser['id'], $voteType);
        }

        // Record rate limit
        $this->db->recordRateLimit($ipAddress, $currentUser['id'], 'vote');

        $this->sendSuccess(['message' => 'Vote recorded successfully']);
    }

    private function getClientIp() {
        $ipKeys = ['HTTP_X_FORWARDED_FOR', 'HTTP_X_REAL_IP', 'HTTP_CLIENT_IP', 'REMOTE_ADDR'];
        
        foreach ($ipKeys as $key) {
            if (!empty($_SERVER[$key])) {
                $ips = explode(',', $_SERVER[$key]);
                $ip = trim($ips[0]);
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                    return $ip;
                }
            }
        }
        
        return $_SERVER['REMOTE_ADDR'] ?? '0.0.0.0';
    }

    private function sendSuccess($data) {
        echo json_encode(['success' => true, 'data' => $data]);
        exit;
    }

    private function sendError($message, $code = 400) {
        http_response_code($code);
        echo json_encode(['success' => false, 'error' => $message]);
        exit;
    }
}

// Handle the request
$handler = new CommentHandler();
$handler->handleRequest();
?>
