<?php
/**
 * Database Connection and Helper Functions
 * For A. A. Chips' Obsidian-Quartz Comments System
 */

class CommentDatabase {
    private static $instance = null;
    private $pdo;
    private $config;

    private function __construct() {
        $this->config = include __DIR__ . '/config.php';
        $this->connect();
    }

    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    private function connect() {
        $dbConfig = $this->config['database'];
        
        try {
            $dsn = "mysql:host={$dbConfig['host']};dbname={$dbConfig['dbname']};charset={$dbConfig['charset']}";
            $this->pdo = new PDO($dsn, $dbConfig['username'], $dbConfig['password'], $dbConfig['options']);
        } catch (PDOException $e) {
            error_log("Database connection failed: " . $e->getMessage());
            throw new Exception("Database connection failed");
        }
    }

    public function getPDO() {
        return $this->pdo;
    }

    public function getConfig($key = null) {
        if ($key === null) {
            return $this->config;
        }
        
        $keys = explode('.', $key);
        $value = $this->config;
        
        foreach ($keys as $k) {
            if (!isset($value[$k])) {
                return null;
            }
            $value = $value[$k];
        }
        
        return $value;
    }

    // User management
    public function createOrUpdateUser($googleId, $email, $name, $pictureUrl = null) {
        $sql = "INSERT INTO users (google_id, email, name, picture_url) 
                VALUES (:google_id, :email, :name, :picture_url)
                ON DUPLICATE KEY UPDATE 
                email = VALUES(email), 
                name = VALUES(name), 
                picture_url = VALUES(picture_url),
                updated_at = CURRENT_TIMESTAMP";
        
        $stmt = $this->pdo->prepare($sql);
        $stmt->execute([
            'google_id' => $googleId,
            'email' => $email,
            'name' => $name,
            'picture_url' => $pictureUrl
        ]);
        
        return $this->getUserByGoogleId($googleId);
    }

    public function getUserByGoogleId($googleId) {
        $sql = "SELECT * FROM users WHERE google_id = :google_id AND is_banned = FALSE";
        $stmt = $this->pdo->prepare($sql);
        $stmt->execute(['google_id' => $googleId]);
        return $stmt->fetch();
    }

    public function getUserById($userId) {
        $sql = "SELECT * FROM users WHERE id = :id AND is_banned = FALSE";
        $stmt = $this->pdo->prepare($sql);
        $stmt->execute(['id' => $userId]);
        return $stmt->fetch();
    }

    // Comment management
    public function createComment($postSlug, $userId, $content, $parentId = null, $ipAddress = null, $userAgent = null) {
        $spamScore = $this->calculateSpamScore($content);
        $isSpam = $spamScore >= $this->getConfig('spam.spam_threshold');
        $isApproved = !$this->getConfig('comments.require_approval') && !$isSpam;

        $sql = "INSERT INTO comments (post_slug, user_id, parent_id, content, is_approved, is_spam, spam_score, ip_address, user_agent) 
                VALUES (:post_slug, :user_id, :parent_id, :content, :is_approved, :is_spam, :spam_score, :ip_address, :user_agent)";
        
        $stmt = $this->pdo->prepare($sql);
        $stmt->execute([
            'post_slug' => $postSlug,
            'user_id' => $userId,
            'parent_id' => $parentId,
            'content' => $content,
            'is_approved' => $isApproved,
            'is_spam' => $isSpam,
            'spam_score' => $spamScore,
            'ip_address' => $ipAddress,
            'user_agent' => $userAgent
        ]);
        
        return $this->pdo->lastInsertId();
    }

    public function getComments($postSlug, $page = 1, $limit = null) {
        if ($limit === null) {
            $limit = $this->getConfig('comments.comments_per_page');
        }
        
        $offset = ($page - 1) * $limit;
        
        $sql = "SELECT c.*, u.name, u.picture_url, u.google_id,
                       (SELECT COUNT(*) FROM comment_votes cv WHERE cv.comment_id = c.id AND cv.vote_type = 'like') as likes,
                       (SELECT COUNT(*) FROM comment_votes cv WHERE cv.comment_id = c.id AND cv.vote_type = 'dislike') as dislikes
                FROM comments c 
                JOIN users u ON c.user_id = u.id 
                WHERE c.post_slug = :post_slug 
                AND c.is_approved = TRUE 
                AND c.is_spam = FALSE 
                AND c.parent_id IS NULL
                ORDER BY c.created_at DESC 
                LIMIT :limit OFFSET :offset";
        
        $stmt = $this->pdo->prepare($sql);
        $stmt->bindValue('post_slug', $postSlug);
        $stmt->bindValue('limit', $limit, PDO::PARAM_INT);
        $stmt->bindValue('offset', $offset, PDO::PARAM_INT);
        $stmt->execute();
        
        $comments = $stmt->fetchAll();
        
        // Get replies for each comment
        foreach ($comments as &$comment) {
            $comment['replies'] = $this->getReplies($comment['id']);
        }
        
        return $comments;
    }

    public function getReplies($parentId, $depth = 0) {
        $maxDepth = $this->getConfig('comments.max_thread_depth');
        if ($depth >= $maxDepth) {
            return [];
        }
        
        $sql = "SELECT c.*, u.name, u.picture_url, u.google_id,
                       (SELECT COUNT(*) FROM comment_votes cv WHERE cv.comment_id = c.id AND cv.vote_type = 'like') as likes,
                       (SELECT COUNT(*) FROM comment_votes cv WHERE cv.comment_id = c.id AND cv.vote_type = 'dislike') as dislikes
                FROM comments c 
                JOIN users u ON c.user_id = u.id 
                WHERE c.parent_id = :parent_id 
                AND c.is_approved = TRUE 
                AND c.is_spam = FALSE 
                ORDER BY c.created_at ASC";
        
        $stmt = $this->pdo->prepare($sql);
        $stmt->execute(['parent_id' => $parentId]);
        
        $replies = $stmt->fetchAll();
        
        // Recursively get replies to replies
        foreach ($replies as &$reply) {
            $reply['replies'] = $this->getReplies($reply['id'], $depth + 1);
        }
        
        return $replies;
    }

    public function getCommentCount($postSlug) {
        $sql = "SELECT COUNT(*) FROM comments 
                WHERE post_slug = :post_slug 
                AND is_approved = TRUE 
                AND is_spam = FALSE";
        
        $stmt = $this->pdo->prepare($sql);
        $stmt->execute(['post_slug' => $postSlug]);
        return $stmt->fetchColumn();
    }

    // Spam detection
    private function calculateSpamScore($content) {
        if (!$this->getConfig('spam.enable_spam_detection')) {
            return 0.0;
        }
        
        $sql = "SELECT pattern, pattern_type, weight FROM spam_patterns WHERE is_active = TRUE";
        $stmt = $this->pdo->prepare($sql);
        $stmt->execute();
        $patterns = $stmt->fetchAll();
        
        $score = 0.0;
        $content_lower = strtolower($content);
        
        foreach ($patterns as $pattern) {
            $match = false;
            
            switch ($pattern['pattern_type']) {
                case 'keyword':
                    $match = strpos($content_lower, strtolower($pattern['pattern'])) !== false;
                    break;
                case 'regex':
                    $match = preg_match('/' . $pattern['pattern'] . '/i', $content);
                    break;
                case 'url':
                    $match = preg_match('/' . $pattern['pattern'] . '/i', $content);
                    break;
            }
            
            if ($match) {
                $score += $pattern['weight'];
            }
        }
        
        return min($score, 1.0); // Cap at 1.0
    }

    // Rate limiting
    public function checkRateLimit($ipAddress, $userId, $actionType) {
        if (!$this->getConfig('spam.enable_rate_limiting')) {
            return true;
        }
        
        $limitKey = "rate_limits.{$actionType}_per_hour";
        $limit = $this->getConfig($limitKey);
        
        if (!$limit) {
            return true;
        }
        
        $sql = "SELECT COUNT(*) FROM rate_limits 
                WHERE (ip_address = :ip_address OR user_id = :user_id)
                AND action_type = :action_type 
                AND window_start > DATE_SUB(NOW(), INTERVAL 1 HOUR)";
        
        $stmt = $this->pdo->prepare($sql);
        $stmt->execute([
            'ip_address' => $ipAddress,
            'user_id' => $userId,
            'action_type' => $actionType
        ]);
        
        $count = $stmt->fetchColumn();
        return $count < $limit;
    }

    public function recordRateLimit($ipAddress, $userId, $actionType) {
        $sql = "INSERT INTO rate_limits (ip_address, user_id, action_type) 
                VALUES (:ip_address, :user_id, :action_type)";
        
        $stmt = $this->pdo->prepare($sql);
        $stmt->execute([
            'ip_address' => $ipAddress,
            'user_id' => $userId,
            'action_type' => $actionType
        ]);
    }

    // Voting
    public function addVote($commentId, $userId, $voteType) {
        $sql = "INSERT INTO comment_votes (comment_id, user_id, vote_type) 
                VALUES (:comment_id, :user_id, :vote_type)
                ON DUPLICATE KEY UPDATE vote_type = VALUES(vote_type)";
        
        $stmt = $this->pdo->prepare($sql);
        return $stmt->execute([
            'comment_id' => $commentId,
            'user_id' => $userId,
            'vote_type' => $voteType
        ]);
    }

    public function removeVote($commentId, $userId) {
        $sql = "DELETE FROM comment_votes WHERE comment_id = :comment_id AND user_id = :user_id";
        $stmt = $this->pdo->prepare($sql);
        return $stmt->execute([
            'comment_id' => $commentId,
            'user_id' => $userId
        ]);
    }

    public function getUserVote($commentId, $userId) {
        $sql = "SELECT vote_type FROM comment_votes WHERE comment_id = :comment_id AND user_id = :user_id";
        $stmt = $this->pdo->prepare($sql);
        $stmt->execute([
            'comment_id' => $commentId,
            'user_id' => $userId
        ]);
        return $stmt->fetchColumn();
    }
}
