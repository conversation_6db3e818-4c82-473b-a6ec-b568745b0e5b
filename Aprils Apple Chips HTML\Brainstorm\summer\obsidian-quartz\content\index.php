<?php
// Auto-generated dynamic content index
// Shows posts from content root directory

// Load path helper and configuration with fallback
$pathPrefix = '../';
if (file_exists(__DIR__ . '/' . $pathPrefix . 'path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . 'path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . 'config.php';
} elseif (file_exists(__DIR__ . '/' . $pathPrefix . '../path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . '../path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . '../config.php';
} else {
    die('Could not find path-helper.php');
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'Welcome to my Digital Garden - A. A. Chips';
$meta_description = 'Personal stories, advocacy work, and reflections';
$meta_keywords = 'A. A. Chips, blog, personal stories, advocacy';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$related_posts = [
    ['title' => 'Alienation', 'url' => PathHelper::getCategoryIndexPath('alienation'), 'excerpt' => 'Browse all Alienation posts'],
    ['title' => 'Climate', 'url' => PathHelper::getCategoryIndexPath('climate'), 'excerpt' => 'Browse all Climate posts'],
    ['title' => 'Humor', 'url' => PathHelper::getCategoryIndexPath('humor'), 'excerpt' => 'Browse all Humor posts'],
    ['title' => 'Inspiration', 'url' => PathHelper::getCategoryIndexPath('inspiration'), 'excerpt' => 'Browse all Inspiration posts'],
    ['title' => 'Journal', 'url' => PathHelper::getCategoryIndexPath('journal'), 'excerpt' => 'Browse all Journal posts'],
    ['title' => 'Judaism', 'url' => PathHelper::getCategoryIndexPath('judaism'), 'excerpt' => 'Browse all Judaism posts'],
    ['title' => 'Kitchen', 'url' => PathHelper::getCategoryIndexPath('kitchen'), 'excerpt' => 'Browse all Kitchen posts'],
    ['title' => 'Ptsd Myth', 'url' => PathHelper::getCategoryIndexPath('ptsd-myth'), 'excerpt' => 'Browse all Ptsd Myth posts'],
    ['title' => 'Street', 'url' => PathHelper::getCategoryIndexPath('street'), 'excerpt' => 'Browse all Street posts'],
    ['title' => 'Writings', 'url' => PathHelper::getCategoryIndexPath('writings'), 'excerpt' => 'Browse all Writings posts'],
];

// Content root posts data
$content_posts = array (
  0 => 
  array (
    'title' => 
    array (
    ),
    'author' => 'A. A. Chips',
    'date' => '2025-05-20',
    'excerpt' => 'I originally put this together as part of a birthday package for a young person I don\'t get to see anymore since running away. I am updating it and repurposing it five years later. I have lifelong delays and challenges with every day things, and this is the equilibrium that works for me. So maybe sharing it may be helpful for someone else.',
    'url' => 'home-tour.php',
    'tags' => 
    array (
      0 => 'journal',
      1 => 'personal',
      2 => 'relationships',
    ),
    'filename' => 'home-tour',
    'thumbnail' => NULL,
  ),
  1 => 
  array (
    'title' => '100 Things I Know About Myself',
    'author' => 'A. A. Chips',
    'date' => NULL,
    'excerpt' => 'This was an exercise for our stand up comedy support group. I hope reading through it doesn\'t cause you to eat rocks.',
    'url' => '100-things-about-myself.php',
    'tags' => 
    array (
      0 => 'personal',
      1 => 'writings',
    ),
    'filename' => '100-things-about-myself',
    'thumbnail' => NULL,
  ),
  2 => 
  array (
    'title' => 'A Day of Joy for Incarcerated Moms',
    'author' => 'A. A. Chips',
    'date' => '2025-05-31',
    'excerpt' => 'May 31st marked the annual Mother Teen Day Retreat at the Swannanoa Women\'s Correctional Facility, a deeply meaningful event orchestrated by a coalition of dedicated faith communities, including my own. This retreat isn\'t just another day; it\'s a beacon of hope anticipated throughout the year by the incarcerated mothers, representing their only annual opportunity to connect, face-to-face, with their children.',
    'url' => 'mom-teen-retreat.php',
    'tags' => 
    array (
      0 => 'advocacy',
      1 => 'teens',
      2 => 'journal',
      3 => 'personal',
    ),
    'filename' => 'mom-teen-retreat',
    'thumbnail' => '../../img/wccw-sign.jpg',
  ),
  3 => 
  array (
    'title' => 'About Me - A. A. Chips
"Date:": 5/22/2025',
    'author' => 'A. A. Chips',
    'date' => NULL,
    'excerpt' => 'Here are some blurbs about me.',
    'url' => 'about-me.php',
    'tags' => 
    array (
    ),
    'filename' => 'about-me',
    'thumbnail' => NULL,
  ),
  4 => 
  array (
    'title' => 'Guide to leaving really bad testimonials about Apple Chips',
    'author' => 'A. A. Chips',
    'date' => NULL,
    'excerpt' => 'I hope you enjoyed your apple chips. I have a small request. I am asking people to leave testimonials on the website about their chip experience. But here’s the thing. When it’s positive testimonials. They are really boring. I am asking people I trust to leave really scathing bad testimonials blaming the apple chips for their problems.',
    'url' => 'bad-chip-testimonial-guide.php',
    'tags' => 
    array (
      0 => 'aachips',
    ),
    'filename' => 'bad-chip-testimonial-guide',
    'thumbnail' => NULL,
  ),
  5 => 
  array (
    'title' => 'I am a founding patron at the 12 Baskets Café. Here is what that means to me.',
    'author' => 'A. A. Chips',
    'date' => '2025-06-06',
    'excerpt' => 'I am a founding patron at the 12 Baskets Café. Here is what that means to me.',
    'url' => 'founding-patron-12b.php',
    'tags' => 
    array (
      0 => 'homeless',
      1 => 'advocacy',
      2 => 'alienation',
      3 => 'accessibility',
      4 => 'a11y',
      5 => 'april',
      6 => 'CompassionateCities',
    ),
    'filename' => 'founding-patron-12b',
    'thumbnail' => NULL,
  ),
  6 => 
  array (
    'title' => 'Table of Contents - A. A. Chips',
    'author' => NULL,
    'date' => '2025-05-22',
    'excerpt' => 'Here\'s a list with all the contents of the site. Good luck, I wish you the best.',
    'url' => 'contents.php',
    'tags' => 
    array (
    ),
    'filename' => 'contents',
    'thumbnail' => NULL,
  ),
  7 => 
  array (
    'title' => 'I\'m an alienated family member whose actually wonderful',
    'author' => 'A. A. Chips',
    'date' => '2025-05-20',
    'excerpt' => 'Hello. I\'m an alienated family member whose actually wonderful. If you need someone supportive in your life. Who believes in you, and will deliver helpful content. Look no further and subscribe. And maybe comment some questions, video ideas, or a bit about yourself.',
    'url' => 'alienated-family-member.php',
    'tags' => 
    array (
      0 => 'journal',
      1 => 'personal',
      2 => 'relationships',
    ),
    'filename' => 'alienated-family-member',
    'thumbnail' => NULL,
  ),
  8 => 
  array (
    'title' => 'Chip Off the Old Block',
    'author' => NULL,
    'date' => '2025-05-20',
    'excerpt' => 'Recently I was invited to participate in a local art project called Unlabel Me. This project addresses the stigmas and labels that are used to dehumanize us, and break through them. I am planning to speak for their showcase event on June 13 for 2-4 minutes. I was given a prompt with several open ended reflection questions.',
    'url' => 'chip-off-old-block.php',
    'tags' => 
    array (
      0 => 'personal',
      1 => 'identity',
      2 => 'family',
      3 => 'reflection',
      4 => 'draft',
    ),
    'filename' => 'chip-off-old-block',
    'thumbnail' => NULL,
  ),
  9 => 
  array (
    'title' => 'I like my space, but someone or someones need me to show up in the world more',
    'author' => 'A. A. Chips',
    'date' => '2025-05-20',
    'excerpt' => 'I value my equilibrium and independence. I love being in my space in quiet, and don\'t crave leaving, or get the dreaded FOMO. I need time alone to process emotions. I\'ve developed strong methods for maintaining my spiritual and emotional balance. I\'ve retreated from past friendships to protect my energy, also due to past experiences.',
    'url' => 'come-to-me.php',
    'tags' => 
    array (
      0 => 'journal',
      1 => 'personal',
      2 => 'relationships',
    ),
    'filename' => 'come-to-me',
    'thumbnail' => NULL,
  ),
  10 => 
  array (
    'title' => 'Hadestown. Teen Edition. My Review.',
    'author' => 'A. A. Chips',
    'date' => '2025-05-20',
    'excerpt' => 'Someone I know was performing in Hadestown. Teen Edition. I got gifted tickets. I went to see it. I am not a theater or musical person, but I listen to musical soundtracks and knew every other word. There was not a dry eye that night.',
    'url' => 'hadestown-review.php',
    'tags' => 
    array (
      0 => 'journal',
    ),
    'filename' => 'hadestown-review',
    'thumbnail' => '../../img/hadestown.jpg',
  ),
  11 => 
  array (
    'title' => 'How Are You?',
    'author' => 'A. A. Chips',
    'date' => '2025-05-20',
    'excerpt' => 'How are you? I struggle with this question, and it\'s a lot easier and empowering to share this information if someone asks.',
    'url' => 'how-are-you.php',
    'tags' => 
    array (
      0 => 'journal',
      1 => 'personal',
    ),
    'filename' => 'how-are-you',
    'thumbnail' => NULL,
  ),
  12 => 
  array (
    'title' => 'Gift of Van Life',
    'author' => 'A. A. Chips',
    'date' => '2025-05-11',
    'excerpt' => '## Van Life: A Gift with Challenges Living in my van is a deeply enjoyable and enriching experience. I’ve grown to love winter, finding cozy ways to...',
    'url' => 'gift-of-van-life.php',
    'tags' => 
    array (
      0 => 'homeless',
      1 => 'advocacy',
      2 => 'alienation',
      3 => 'accessibility',
      4 => 'a11y',
      5 => 'april',
      6 => 'CompassionateCities',
    ),
    'filename' => 'gift-of-van-life',
    'thumbnail' => NULL,
  ),
  13 => 
  array (
    'title' => 'Healing Homelessness - Reflection',
    'author' => 'A. A. Chips',
    'date' => '2025-05-01',
    'excerpt' => 'In January 2017, I packed my car and drove south without telling anyone. To some, it looked like I’d "chosen" homelessness. The truth? I was running to survive.',
    'url' => 'healing-homelessness.php',
    'tags' => 
    array (
      0 => 'homeless',
      1 => 'writings',
    ),
    'filename' => 'healing-homelessness',
    'thumbnail' => NULL,
  ),
  14 => 
  array (
    'title' => 'I am not Smee',
    'author' => 'A. A. Chips',
    'date' => NULL,
    'excerpt' => 'I\'ve got to address something. I was told today I was giving off heavy Mr. Smee vibes, as in the supporting antagonist from Hook. I want to put on record, that I look nothing like Mr. Smee. I had a picture taken to prove how little of a resemblance there is. I am not Smee.
"Date:": 5/15/2025',
    'url' => 'i-am-not-smee.php',
    'tags' => 
    array (
    ),
    'filename' => 'i-am-not-smee',
    'thumbnail' => NULL,
  ),
  15 => 
  array (
    'title' => 'I cook most of my meals',
    'author' => 'A. A. Chips',
    'date' => '2025-05-20',
    'excerpt' => 'Experiences and strategies for cooking while experiencing housing instability',
    'url' => 'i-prefer-cooking.php',
    'tags' => 
    array (
      0 => 'cooking',
      1 => 'self-sufficiency',
      2 => 'food',
      3 => 'applechipkitchen',
    ),
    'filename' => 'i-prefer-cooking',
    'thumbnail' => NULL,
  ),
  16 => 
  array (
    'title' => 'Isaiah 43 1-2',
    'author' => 'Bible',
    'date' => NULL,
    'excerpt' => 'Do not fear, for I have redeemed you; I have called you by name, you are mine.',
    'url' => 'isaiah4312.php',
    'tags' => 
    array (
      0 => 'bible',
      1 => 'repost',
      2 => 'library',
      3 => 'music',
      4 => 'love-story',
    ),
    'filename' => 'isaiah4312',
    'thumbnail' => NULL,
  ),
  17 => 
  array (
    'title' => 'My Reading Journey after Traumatic Brain Injury',
    'author' => 'A. A. Chips',
    'date' => '2025-05-20',
    'excerpt' => 'My relationship with reading has fundamentally changed after a series of traumatic brain injuries. Dense blocks of text present a genuine barrier to my learning.',
    'url' => 'reading-after-tbi.php',
    'tags' => 
    array (
      0 => 'journal',
      1 => 'personal',
      2 => 'readings',
    ),
    'filename' => 'reading-after-tbi',
    'thumbnail' => NULL,
  ),
  18 => 
  array (
    'title' => 'Nothing About Me Without Me - Center for Disability Rights',
    'author' => NULL,
    'date' => NULL,
    'excerpt' => '#articles #resources #library #disability #lawyer #hhs #casemgmt --- Author:: Sydney Chasteen; Center for Disability Rights Date:: Unknown Key:: Publi...',
    'url' => 'nothing-about-me-without-me.php',
    'tags' => 
    array (
    ),
    'filename' => 'nothing-about-me-without-me',
    'thumbnail' => NULL,
  ),
  19 => 
  array (
    'title' => 'One year into being a Kindergarten teacher, and what that\'s like.',
    'author' => 'A. A. Chips',
    'date' => '2025-05-11',
    'excerpt' => 'It\'s been one year since I started teaching Kindergarten. I am reflecting on what that\'s like.',
    'url' => 'one-year-in.php',
    'tags' => 
    array (
      0 => 'kindergarten',
      1 => 'teaching',
      2 => 'journal',
      3 => 'personal',
    ),
    'filename' => 'one-year-in',
    'thumbnail' => NULL,
  ),
  20 => 
  array (
    'title' => 'For two years I have chosen sobriety. Here\'s what that is about.',
    'author' => 'A. A. Chips',
    'date' => '2025-05-01',
    'excerpt' => 'This probably is not what you think it is. For a large portion of my adult life I\'ve been a daily pot smoker. I\'m not a fan of drinking alcohol beyond...',
    'url' => 'sobriety.php',
    'tags' => 
    array (
      0 => 'writings',
      1 => 'personal',
    ),
    'filename' => 'sobriety',
    'thumbnail' => NULL,
  ),
  21 => 
  array (
    'title' => 'How to Support April\'s Apple Chips',
    'author' => 'A. A. Chips',
    'date' => '2025-05-01',
    'excerpt' => 'Help a chip off the old block get their business off the ground.. Every cold season for the past six years I have made apple chips. Most of these are given out for free, many of them are sold. In the off-season, I work on the administration and side projects.',
    'url' => 'support-aachips.php',
    'tags' => 
    array (
      0 => 'aachips',
    ),
    'filename' => 'support-aachips',
    'thumbnail' => NULL,
  ),
  22 => 
  array (
    'title' => 'Since the toilet paper scares in 2020, I have washed my behind with a squeeze bottle originally intended for barbecue sauce.',
    'author' => 'A. A. Chips',
    'date' => NULL,
    'excerpt' => '',
    'url' => 'bottle-bidet.php',
    'tags' => 
    array (
    ),
    'filename' => 'bottle-bidet',
    'thumbnail' => NULL,
  ),
  23 => 
  array (
    'title' => 'This was a top rated response on Reddit.com to the question, "What did you learn about an odd unique person that made you say, \'I get you now\'?" This describes me very well.',
    'author' => 'Friendship_Prevails on reddit',
    'date' => NULL,
    'excerpt' => '*I found this story on a response to a Reddit post. It\'s really great and echoes my own experience of disconnecting from everybody and everything for ...',
    'url' => 'reddit-response-schizophrenia.php',
    'tags' => 
    array (
      0 => 'homeless',
      1 => 'advocacy',
      2 => 'april',
      3 => 'memes',
    ),
    'filename' => 'reddit-response-schizophrenia',
    'thumbnail' => NULL,
  ),
);

// Generate content
ob_start();
?>
<div class="content-index">
    <header class="content-header">
        <h1><?php echo htmlspecialchars($page_title); ?></h1>
    </header>

    <div class="content-description">
        <?php echo <<<'HTML'
<p>Hello <a href="http://upworthy.com/rednote-letters-from-li-hua" class="external-link">Li Hua</a>,</p>

<p>You can call me A. A. Chips. But let's scrap the formalities. You can call me A. A. for short. I am a real life super villain, and if anyone else says otherwise, they are wrong.</p>

<img src="../img/were-all-the-villain.png">

<p>I am sorry for not returning your letters sooner. When I received the pile of letters in grade school, I thought it was homework. and most of us don't do our homework in America. As a grown up, when your letters showed up in the mail, I thought they were tax-collections and spam advertisements. Most of us don't look at our mail as it is a source of anxiety and dread.</p>

<p>This is probably the best way to reach me. I am here now. I have so much I wish to share with you. So much of my precious data. All the corporations here just want me for my data. They only care about one thing, and it's disgusting. But now that you are here, I want to give you all, of my delicious data. Just you.</p>

<p>A friend told me once:</p>

<p>"Sometimes you have to make peace with the fact that you are the villain in someone else's story, even if you thought you were doing the right thing. You don't get to tell them how to narrate their experience."</p>

<p>Sometimes people pretend that you are a bad person so they don't feel guilty about the things they did to you, too.</p>

<p>I'm putting this together as an experiment, to upload my brain and work into a website, in a fun and interactive way.  This is a maze of content, which you can follow to the cheese at the end, or go here for a <a href="contents.php">contents</a>.</p>

<p><a href="about-me.php">For more about me, please go here</a>..</p>

<img src="../img/rosegrandpa.png" alt="rose grandpa quartz steven universe." width="300">

<p>If you are interested in how I put this information together, I highly encourage reading more about <a href="content/obsidian-vault.php" class="internal-link">How I use Obsidian.md and Quartz Publishing</a>.</p>

<p>If anything I say or do ruins your day, one way to express thanks is to give me money. You can do that at the bottom left button labeled 'Support.' It is connected to my <a href="https://www.ko-fi.com/aachips" target="_blank" rel="noopener noreferrer">Ko-Fi page where patrons can make a one-time or monthly recurring pledge towards my work</a>.</p>

<p>Most of what i do is a labor of love. Money helps the world go round, and amplifies what I am doing. It also allows me to financially support people and projects close to my heart. <a href="content/public/crowdfunding-campaign-a-a-chips.php" class="internal-link">For detailed information about my crowdfunding campaign, please go here.</a></p>

<p><strong><em>Word of caution</strong>: If you accept bad information about me. Without question, criticism, or receipt. It may be at your own peril. You are free to believe what you wish to believe. I do not care, and will not come after you. But Physics might. Caution.</em></p>


<ul>
<ul><li>
<p><a href="about-me.php" class="internal-link">Here are some blurbs about me and the work that I am doing.</a></li></p>
<p><li></p>
<p><a href="https://www.aachips.co/heartwarmers" target="_blank">Here is the current website for Heartwarmers, the interactive platform I am looking for <strike>minions</strike> volunteers to help build.</a></li></p>
<p><li></p>
<p><a href="alienation/dad-wasnt-who-they-said.php">This is a video by Ryan Thomas which explains his journey of discovering the truth about his father after years of being alienated and being told lies about who he was.</a></li></p>
<p><li></p>
<p><a href="100-things-about-myself.php">This is an exercise I am doing for our stand up comedy group that meets on Friday nights. I am sharing 100 things I know about myself. I am up to maybe 35.</a></li></p>
<p><li></p>
<p><a href="bed-free.php" class="internal-link">I've been bed-free for years. Here are three different types of Hammocks you can set up where you sleep.</a></li></p>
<p><li></p>
<p><a href="bite-sized-learning.php" class="internal-link">Bite-Sized Learning for Everyone: Introducing Knowledge "Chips"</a></li></p>
<p><li></p>
<p><a href="chip-off-the-old-block.php" class="internal-link">Chip Off the Old Block</a></li></p>
<p><li></p>
<p><a href="come-to-me.php">This is an open invitation, rules, and guidelines for new connection in my life. While I enjoy my space, and am not seeking anything from others currently, I know there are people in the world who need what I have to offer. If someone seeks to get close to me in some way, I will likely share this to ensure everyone is on the same page.</a></li></p>
<p><li></p>
<p><a href="eight-months-after-hurricane-helene-hit-us-i-wasn-t-impacted-hard-everyone-around-me-was.php" class="internal-link">Eight months after Hurricane Helene hit us. I wasn't impacted hard. Everyone around me was.</a></li></p>
<p><li></p>
<p><a href="founding-patron-12b.php" class="internal-link">I am a founding patron at the 12 Baskets Café. Here is what that means to me.</a></li></p>
<p><li></p>
<p><a href="hadestown-review.php" class="internal-link">Hadestown. Teen Edition. My Review.</a></li></p>
<p><li></p>
<p><a href="here-s-a-tour-of-my-adult-adhd-life-for-your-birthday.php" class="internal-link">Here's a tour of my adult ADHD life for your birthday.</a></li></p>
<p><li></p>
<p><a href="i-am-not-smee.php" class="internal-link">I am not Smee.</a><p>I've got to address something. I was told today I was giving off heavy Mr. Smee vibes, as in the supporting antagonist from Hook. I want to put on record, that I look nothing like Mr. Smee. I had a picture taken to prove how little of a resemblance there is. I am not Smee.</p></li></p>
<p><li></p>
<p><a href="i-dont-want-to-talk-for-the-rest-of-my-life.php" class="internal-link">I dont want to talk for the rest of my life</a></li></p>
<p><li></p>
<p><a href="i-like-my-space-but-someone-or-someones-need-me-to-show-up-in-the-world-more.php" class="internal-link">I like my space, but someone or someones need me to show up in the world more</a></li></p>
<p><li></p>
<p><a href="i-m-a-proud-sephardic-jew-who-supports-a-free-palestine-there-is-nothing-jewish-about-israel-s-actions.php" class="internal-link">I'm a proud Sephardic Jew who supports a free Palestine. There is nothing Jewish about Israel's actions.</a></li></p>
<p><li></p>
<p><a href="i-m-an-alienated-family-member-whose-actually-wonderful.php" class="internal-link">I'm an alienated family member whose actually wonderful</a></li></p>
<p><li></p>
<p><a href="why-i-can-t-get-a-passport-right-now.php" class="internal-link">Why I can't get a passport right now</a></li></p>
<p><li></p>
<p><a href="bottle-bidet.php" class="internal-link">Since the toilet paper scares in 2020, I have washed my behind with a squeeze bottle originally intended for barbecue sauce.</a></li></p>
<p><li></p>
<p><a href="bad-chip-testimonial-guide.php" class="internal-link">Guide to leaving really bad testimonials about Apple Chips</a></li></p>
<p><li></p>
<p><a href="how-are-you.php" class="internal-link">Response to 'How Are You?</a></li></p>
<p><li></p>
<p><a href="i-prefer-cooking.php" class="internal-link">I don't like eating out very much. It brings me a lot more joy to cook with people</a></li></p>
<p><li></p>
<p><a href="humor/index.php">Here's a bunch of funny things I've saved and bookmarked off the internet.</a></li></p>
<p><li></p>
<p><a href="inspiration/index.php">Here's a bunch of inspirational things I've saved and bookmarked off the internet.</a></li></p>
<p></ul></p>

<ul>
<p><li></p>
<p><a href="alienation/index.php">Here is an entire vault of content and resources regarding family and parental alienation. This is a topic that is likely very close to many that would be participating in an event like this. And it almost never is talked about.</a></li></p>
<p><li></p>
<p><a href="climate/index.php">Here is an entire vault of content and resources regarding climate change. This is a topic that is likely very close to many that would be participating in an event like this. And it almost never is talked about.</a></li></p>
<p><li></p>
<p><a href="humor/index.php">Here is an entire vault of content and resources regarding humor. This is a topic that is likely very close to many that would be participating in an event like this. And it almost never is talked about.</a></li></p>
<p><li></p>
<p><a href="inspiration/index.php">Here is an entire vault of content and resources regarding inspiration. This is a topic that is likely very close to many that would be participating in an event like this. And it almost never is talked about.</a></li></p>
<p><li></p>
<p><a href="journal/index.php">Here is an entire vault of content and resources regarding journaling. This is a topic that is likely very close to many that would be participating in an event like this. And it almost never is talked about.</a></li></p>
<p><li></p>
<p><a href="judaism/index.php">Here is an entire vault of content and resources regarding Judaism. This is a topic that is likely very close to many that would be participating in an event like this. And it almost never is talked about.</a></li></p>
<p><li></p>
<p><a href="kitchen/index.php">Here is an entire vault of content and resources regarding cooking and the kitchen. This is a topic that is likely very close to many that would be participating in an event like this. And it almost never is talked about.</a></li></p>
<p><li></p>
<p><a href="street/index.php">Here is an entire vault of content and resources regarding homelessness and the street. This is a topic that is likely very close to many that would be participating in an event like this. And it almost never is talked about.</a></li></p>
<p><li></p>
<p><a href="writings/index.php">Here is an entire vault of content and resources regarding writing. This is a topic that is likely very close to many that would be participating in an event like this. And it almost never is talked about.</a></li></ul></p>
<p></ul></p>

<img src="../img/you-have-heard-of-me.png" alt="But you have heard of me." width="400">



<img src="img/offensive.jpg" alt="offensive.jpg"><img src="img/rest-in-it.jpg" alt="rest-in-it.jpg">
<img src="img/lurkLaughLoathe.png" alt="lurkLaughLoathe.png">
<img src="img/cat.jpg" alt="cat.jpg">
HTML;
        ?>
    </div>

    <div class="post-grid">
        <?php foreach ($content_posts as $post): ?>
            <div class="post-card">
                <a href="<?php echo htmlspecialchars($post['url']); ?>" class="post-card-link">
                <div class="post-card-thumbnail">
                    <?php if (isset($post['thumbnail']) && $post['thumbnail']): ?>
                        <?php if (preg_match('/^https?:\/\//', $post['thumbnail'])): ?>
                            <img src="<?php echo htmlspecialchars($post['thumbnail']); ?>" 
                                 alt="<?php echo htmlspecialchars($post['title']); ?>" class="post-thumb-img">
                        <?php else: ?>
                            <img src="<?php echo $paths['base_path']; ?>img/thumbs/<?php echo htmlspecialchars($post['thumbnail']); ?>" 
                                 alt="<?php echo htmlspecialchars($post['title']); ?>" class="post-thumb-img">
                        <?php endif; ?>
                    <?php else: ?>
                        <?php $placeholder_images = ['placeholder1.jpg', 'placeholder2.jpg', 'placeholder3.jpg', 'placeholder4.jpg']; ?>
                        <?php $random_placeholder = $placeholder_images[array_rand($placeholder_images)]; ?>
                        <img src="<?php echo $paths['base_path']; ?>img/thumbs/<?php echo $random_placeholder; ?>" 
                             alt="<?php echo htmlspecialchars($post['title']); ?>" class="post-thumb-img placeholder">
                    <?php endif; ?>
                </div>
                <div class="post-card-content">
                    <h3 class="post-card-title"><?php echo htmlspecialchars($post['title']); ?></h3>
                    <p class="post-card-excerpt"><?php echo htmlspecialchars(is_string($post['excerpt']) ? $post['excerpt'] : ''); ?></p>
                    <div class="post-card-meta">
                        <?php if (isset($post['author']) && $post['author'] && is_string($post['author'])): ?>
                            <span class="post-author">By <?php echo htmlspecialchars($post['author']); ?></span>
                        <?php endif; ?>
                        <?php if (isset($post['date']) && $post['date']): ?>
                            <span class="post-date"><?php echo is_string($post['date']) ? htmlspecialchars($post['date']) : ''; ?></span>
                        <?php endif; ?>
                    </div>
                    <?php if (!empty($post['tags'])): ?>
                        <div class="post-card-tags">
                            <?php foreach ($post['tags'] as $tag): ?>
                                <span class="tag"><?php echo htmlspecialchars($tag); ?></span>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
                </a>
            </div>
        <?php endforeach; ?>
    </div>
</div>
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>