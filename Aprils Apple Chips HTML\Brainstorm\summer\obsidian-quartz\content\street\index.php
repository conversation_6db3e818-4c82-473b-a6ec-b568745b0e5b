<?php
// Auto-generated category index
// Category: street

// Load path helper and configuration with fallback
$pathPrefix = '../';
if (file_exists(__DIR__ . '/' . $pathPrefix . 'path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . 'path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . 'config.php';
} elseif (file_exists(__DIR__ . '/' . $pathPrefix . '../path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . '../path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . '../config.php';
} else {
    die('Could not find path-helper.php');
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'Street Advocacy Index';
$meta_description = 'Street Advocacy Index';
$meta_keywords = 'street, posts, A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$related_posts = [];

// Category posts data
$category_posts = array (
  0 => 
  array (
    'title' => 'Austin experimented with giving people $1,000 a month. They said they spent the no-strings-attached cash mostly on housing.',
    'author' => 'Kenneth Niemeyer - Business Insider',
    'date' => NULL,
    'excerpt' => 'A guaranteed basic income plan in one of Texas\'s largest cities reduced rates of housing insecurity. But some Texas lawmakers are not happy.',
    'url' => 'austin-ubi.php',
    'tags' => 
    array (
      0 => 'homeless',
      1 => 'basicincome',
      2 => 'advocacy',
      3 => 'library',
    ),
    'filename' => 'austin-ubi',
    'thumbnail' => 'https://schoolworkhelper.net/wp-content/uploads/2022/06/Universal-Basic-Income.webp',
  ),
  1 => 
  array (
    'title' => 'Ensuring Mental Wellness for Transgender & Nonbinary Youth with PFLAG',
    'author' => 'Springtide Research Institute',
    'date' => NULL,
    'excerpt' => 'Ensuring the mental wellness of transgender and nonbinary youth is a pressing issue. Organizations like PFLAG play a crucial role in providing support and advocacy.',
    'url' => 'lgbt-youth-wellness.php',
    'tags' => 
    array (
      0 => 'advocacy',
      1 => 'lgbt',
      2 => 'alienation',
    ),
    'filename' => 'lgbt-youth-wellness',
    'thumbnail' => 'https://media.them.us/photos/62745743012ef49d5c175fe8/16:9/w_1920,c_limit/1056596624',
  ),
  2 => 
  array (
    'title' => 'Forgetting the Former Things',
    'author' => 'A. A. Chips & Tamara Puffner',
    'date' => NULL,
    'excerpt' => 'I woke up this morning uneased after having back to back flashbacks over dream. It was a rough morning. I got a lot of solace reading the introduction to the book Forgetting the Former Things by Tamara Puffner and Joyce Hollyday. I\'ve not felt more seen by a passage in a book for a long time.',
    'url' => 'forgetting-former-things.php',
    'tags' => 
    array (
      0 => 'homeless',
      1 => 'advocacy',
      2 => 'alienation',
      3 => 'accessibility',
      4 => 'a11y',
      5 => 'april',
      6 => 'CompassionateCities',
    ),
    'filename' => 'forgetting-former-things',
    'thumbnail' => '../../img/forgetting-the-former-things.webp',
  ),
  3 => 
  array (
    'title' => 'Hobo Code of Ethics',
    'author' => 'Unknown',
    'date' => NULL,
    'excerpt' => 'At the 1889 National Hobo Convention in St. Louis, a strict ethical code was established for all hobos to follow. Here are some tips we could all use, no matter what you carry in your rucksack.',
    'url' => 'hobo-code-ethics.php',
    'tags' => 
    array (
      0 => 'homeless',
      1 => 'inspiration',
      2 => 'CompassionateCities',
      3 => 'resources',
      4 => 'library',
      5 => 'advocacy',
      6 => 'repost',
    ),
    'filename' => 'hobo-code-ethics',
    'thumbnail' => 'https://www.openculture.com/wp-content/uploads/2016/11/hbo-code-1024x586.png',
  ),
  4 => 
  array (
    'title' => 'If you see something, do something. Alternatives to calling the police.',
    'author' => NULL,
    'date' => NULL,
    'excerpt' => 'Calling the police often escalates situations, puts people at risk, and leads to violence. Anytime you seek help from the police, you\'re inviting them into your community and putting people who may already be vulnerable into dangerous situations. Sometimes people feel that calling the police is the only way to deal with problems.',
    'url' => 'see-something-do-something.php',
    'tags' => 
    array (
      0 => 'CompassionateCities',
      1 => 'advocacy',
      2 => 'accessibility',
    ),
    'filename' => 'see-something-do-something',
    'thumbnail' => NULL,
  ),
  5 => 
  array (
    'title' => 'More Than a Meal - The power of sharing tables and street stages',
    'author' => 'A. A. Chips',
    'date' => '2025-06-06',
    'excerpt' => 'Exploring the power of sharing tables and street stages as a form of advocacy and community building.',
    'url' => 'more-than-a-meal.php',
    'tags' => 
    array (
      0 => 'homelessness',
      1 => 'advocacy',
      2 => 'applechipkitchen',
    ),
    'filename' => 'more-than-a-meal',
    'thumbnail' => NULL,
  ),
  6 => 
  array (
    'title' => 'PTSD is Outdated Language',
    'author' => 'A. A. Chips',
    'date' => '2025-05-20',
    'excerpt' => 'Challenging conventional wisdom about PTSD and mental health through research, advocacy, and lived experience. A collection of writings on mental health reform.',
    'url' => 'PTSD is outdated language.php',
    'tags' => 
    array (
      0 => 'mental health',
      1 => 'ptsd',
      2 => 'psychology',
      3 => 'advocacy',
      4 => 'research',
    ),
    'filename' => 'PTSD is outdated language',
    'thumbnail' => 'https://www.banyantreatmentcenter.com/wp-content/uploads/2022/09/ptsd-history.png',
  ),
  7 => 
  array (
    'title' => 'Rethinking Wellness Checks',
    'author' => 'A. A. Chips',
    'date' => NULL,
    'excerpt' => 'We all want our loved ones to be safe, and sometimes that means relying on wellness checks. But for adults with learning disabilities, these checks can have a dangerous downside – a higher risk of being hurt by law enforcement.',
    'url' => 'wellness-checks.php',
    'tags' => 
    array (
      0 => 'homeless',
      1 => 'accessibility',
      2 => 'CompassionateCities',
      3 => 'blog',
      4 => 'parentalalienation',
    ),
    'filename' => 'wellness-checks',
    'thumbnail' => NULL,
  ),
  8 => 
  array (
    'title' => 'Traumatic Brain Injury in Homeless People is Underrecognized',
    'author' => 'Susan Fitzgerald - NeurologyToday',
    'date' => '2025-06-06',
    'excerpt' => 'Traumatic brain injury (TBI) is a common and often underrecognized problem in homeless people, according to a new study.',
    'url' => 'tbi-homeless-research-findings.php',
    'tags' => 
    array (
      0 => 'homelessness',
      1 => 'advocacy',
      2 => 'applechipkitchen',
    ),
    'filename' => 'tbi-homeless-research-findings',
    'thumbnail' => NULL,
  ),
  9 => 
  array (
    'title' => 'A Quick Guide to Navigating Gender',
    'author' => 'A. A. Chips',
    'date' => '2025-05-20',
    'excerpt' => 'A Quick Guide to Navigating Gender',
    'url' => 'navigate-gender.php',
    'tags' => 
    array (
      0 => 'homeless',
      1 => 'advocacy',
      2 => 'alienation',
      3 => 'accessibility',
      4 => 'a11y',
      5 => 'april',
      6 => 'CompassionateCities',
    ),
    'filename' => 'navigate-gender',
    'thumbnail' => 'https://m.media-amazon.com/images/I/61-EywnqF3L._AC_SL1500_.jpg',
  ),
  10 => 
  array (
    'title' => 'Until you\'ve lost the ability to make rational decisions, you retain the ability to make dumb ones.',
    'author' => 'A. A. Chips',
    'date' => '2025-05-20',
    'excerpt' => '### Understanding Supported Decision Making It can be concerning to witness someone in your life making what appear to be consistently poor choices, w...',
    'url' => 'supported-decision-making.php',
    'tags' => 
    array (
      0 => 'homeless',
      1 => 'advocacy',
      2 => 'alienation',
      3 => 'accessibility',
      4 => 'sdm',
    ),
    'filename' => 'supported-decision-making',
    'thumbnail' => NULL,
  ),
  11 => 
  array (
    'title' => 'When "Choice" Isn’t Really a Choice',
    'author' => NULL,
    'date' => '2025-05-20',
    'excerpt' => 'Addressing the misconception that homelessness is a choice and exploring the reality of limited options',
    'url' => 'when-choice-isnt-choice.php',
    'tags' => 
    array (
      0 => 'homelessness',
      1 => 'advocacy',
      2 => 'misconceptions',
      3 => 'personal',
      4 => 'draft',
      5 => 'videoscript',
    ),
    'filename' => 'when-choice-isnt-choice',
    'thumbnail' => NULL,
  ),
  12 => 
  array (
    'title' => 'Responding to Panhandlers',
    'author' => 'A. A. Chips',
    'date' => '2025-05-11',
    'excerpt' => 'When someone on the street with a compelling story asks for money, it can evoke a range of emotions. How do you typically react? Do you hurry past, offer a listening ear, or reach into your pocket?',
    'url' => 'response-panhandlers.php',
    'tags' => 
    array (
      0 => 'homeless',
      1 => 'advocacy',
      2 => 'street',
    ),
    'filename' => 'response-panhandlers',
    'thumbnail' => NULL,
  ),
  13 => 
  array (
    'title' => 'Now is not the time to acquiesce -- Universities Resist Funding Demands in Clash over Academic Freedom',
    'author' => 'A. A. Chips',
    'date' => '2025-04-23',
    'excerpt' => 'Legal experts predict protracted court battles, while advocacy groups warn of a chilling effect on research and teaching. The central question remains: Can universities withstand federal pressure—or will financial threats force compliance?',
    'url' => 'not-time-to-acquiesce.php',
    'tags' => 
    array (
      0 => 'advocacy',
      1 => 'writings',
      2 => 'masscommunication',
    ),
    'filename' => 'not-time-to-acquiesce',
    'thumbnail' => 'https://external-content.duckduckgo.com/iu/?u=https%3A%2F%2Fwww.reuters.com%2Fresizer%2F5sTpyJb9wceqDhkaEb0sBc96DZ8%3D%2F1920x1005%2Fsmart%2Ffilters%3Aquality(80)%2Fcloudfront-us-east-2.images.arcpublishing.com%2Freuters%2F5GPPR5VQ25MLDM6VJFPDHYMHFM.jpg&f=1&nofb=1&ipt=14358ba45aceb0ae1fd64076edd9a2e5d5ea2046762edab26d2e4ea87a343531',
  ),
  14 => 
  array (
    'title' => 'Understanding ADHD in Women and Girls: Key Insights from a Decades-Long Study',
    'author' => 'MissUnderstood: The ADHD in Women Channel',
    'date' => '2024-12-05',
    'excerpt' => 'A recent episode of the Hyperfocus podcast featured a crucial discussion with Dr. Stephen Hinshaw, a leading voice in understanding ADHD in women and girls, largely due to his work on The Berkeley Girls with ADHD Longitudinal Study (BGALs). This decades-long research followed 140 girls with ADHD and a control group into adulthood, yielding significant insights that have shaped our current understanding of the condition in females.',
    'url' => 'adhd-in-women.php',
    'tags' => 
    array (
    ),
    'filename' => 'adhd-in-women',
    'thumbnail' => 'https://www.bphope.com/wp-content/uploads/2023/03/adhd-women-diagnosis-1357134137.jpg',
  ),
  15 => 
  array (
    'title' => 'Secret Agent 23 Skidoo - Tomorrow\'s Cost',
    'author' => 'Secret Agent 23 Skidoo',
    'date' => '2024-11-10',
    'excerpt' => '\'I hope this post gets shared nationally, because I know the story of Hurricane Helene and the devastation it wrought on the communities of Western North Carolina is getting washed away by the flood of events in the news cycle.\'',
    'url' => 'tomorrows-cost.php',
    'tags' => 
    array (
      0 => 'homeless',
      1 => 'disasterrelief',
      2 => 'music',
      3 => 'advocacy',
      4 => 'blog',
    ),
    'filename' => 'tomorrows-cost',
    'thumbnail' => 'https://external-content.duckduckgo.com/iu/?u=https%3A%2F%2Fwww.severe-weather.eu%2Fwp-content%2Fgallery%2Fandrej-news%2Fhurricane-season-2024-forecast-major-storm-helene-florida-us-landfall.jpg&f=1&nofb=1&ipt=d693aff8790d872b9096a555c2a6e40c3a17d5c13a91e3280aca25504998331d',
  ),
  16 => 
  array (
    'title' => 'Preserve the Southside Urban Farm --- Securing a Sustainable Future',
    'author' => 'A. A. Chips',
    'date' => '2024-04-15',
    'excerpt' => 'As an alumni of the Green Opportunities Kitchen Ready Training program at the Southside Kitchen, I cannot stress enough the profound impact this urban farm has had on my life.',
    'url' => 'preserve-southside-farm.php',
    'tags' => 
    array (
      0 => 'climate',
      1 => 'advocacy',
      2 => 'applechipkitchen',
    ),
    'filename' => 'preserve-southside-farm',
    'thumbnail' => '../../img/greenopportunities.jpg',
  ),
  17 => 
  array (
    'title' => 'Employee v Contractor - Understanding the Power Dynamics',
    'author' => 'A. A. Chips',
    'date' => '2024-03-03',
    'excerpt' => 'Here is a blog post I wrote explaining the power dynamics and differences between Employment and Independent Contracting..',
    'url' => 'employee-contractor.php',
    'tags' => 
    array (
      0 => 'aachips',
      1 => 'employment',
      2 => 'advocacy',
      3 => 'accessibility',
      4 => 'applechipkitchen',
    ),
    'filename' => 'employee-contractor',
    'thumbnail' => 'https://external-content.duckduckgo.com/iu/?u=https%3A%2F%2Fwww.theabctest.com%2Fimages%2FHR-Employee-V-Contractor.jpg&f=1&nofb=1&ipt=745c0a9c7b0bb23a655a259101f64e047df08912b55e5175ec1f98959a022da4',
  ),
  18 => 
  array (
    'title' => 'Is the Rent Too Damn High?',
    'author' => NULL,
    'date' => '2023-01-07',
    'excerpt' => 'To comfortably afford that rent using the 30% rule, we\'d need a minimum wage of... *drumroll please*... $37.06 an hour!',
    'url' => 'rent-too-high.php',
    'tags' => 
    array (
      0 => 'CompassionateCities',
      1 => 'advocacy',
      2 => 'disasterrelief',
      3 => 'homeless',
    ),
    'filename' => 'rent-too-high',
    'thumbnail' => 'https://c8.alamy.com/comp/EA4CYW/rent-is-too-damn-high!-poster-in-seattle-usa-EA4CYW.jpg',
  ),
  19 => 
  array (
    'title' => 'Don\'t Make Your Adult Kids Homeless',
    'author' => 'A. A. Chips',
    'date' => '2022-04-15',
    'excerpt' => 'For many young adults, particularly those facing unique challenges, the "nuclear option" of forced eviction can be devastating and counterproductive. As someone who has experienced homelessness firsthand after familial fallout, I offer a different viewpoint rooted in personal experience and a desire for more humane solutions.',
    'url' => 'dont-make-them-homeless.php',
    'tags' => 
    array (
      0 => 'april',
      1 => 'writings',
      2 => 'homeless',
      3 => 'advocacy',
    ),
    'filename' => 'dont-make-them-homeless',
    'thumbnail' => 'https://www.vincentcare.org.au/wp-content/uploads/2019/03/homeless-youth.jpg',
  ),
  20 => 
  array (
    'title' => 'How to Stay Warm - Heartwarmers',
    'author' => 'A. A. Chips',
    'date' => '2018-01-28',
    'excerpt' => 'Hi I used to be homeless. Here are some ways to stay warm. Cotton is most common clothing material. It sucks! Especially if it gets wet. Cold goes right through.',
    'url' => 'stay-warm.php',
    'tags' => 
    array (
      0 => 'homeless',
      1 => 'heartwarmers',
      2 => 'writings',
      3 => 'blog',
      4 => 'advocacy',
      5 => 'climate',
      6 => 'disasterrelief',
    ),
    'filename' => 'stay-warm',
    'thumbnail' => '../../img/heartwarmer-logo.png',
  ),
);

// Generate content
ob_start();
?>
<div class="category-index">
    <header class="category-header">
        <h1><?php echo htmlspecialchars($page_title); ?></h1>
    </header>

    <div class="category-content">
        <?php echo <<<'HTML'
<p>Here is a bunch of advocacy content that doesn't always fit into an acceptable framework or category. If it's about educating about how the world is, or pushing for how the world should be, it's here.</p>


HTML;
        ?>
    </div>

    <div class="post-grid">
        <?php foreach ($category_posts as $post): ?>
            <div class="post-card">
                <a href="<?php echo htmlspecialchars($post['url']); ?>" class="post-card-link">
                <div class="post-card-thumbnail">
                    <?php if (isset($post['thumbnail']) && $post['thumbnail']): ?>
                        <?php if (preg_match('/^https?:\/\//', $post['thumbnail'])): ?>
                            <img src="<?php echo htmlspecialchars($post['thumbnail']); ?>" 
                                 alt="<?php echo htmlspecialchars($post['title']); ?>" class="post-thumb-img">
                        <?php else: ?>
                            <img src="<?php echo $paths['base_path']; ?>img/thumbs/<?php echo htmlspecialchars($post['thumbnail']); ?>" 
                                 alt="<?php echo htmlspecialchars($post['title']); ?>" class="post-thumb-img">
                        <?php endif; ?>
                    <?php else: ?>
                        <?php $placeholder_images = ['placeholder1.jpg', 'placeholder2.jpg', 'placeholder3.jpg', 'placeholder4.jpg']; ?>
                        <?php $random_placeholder = $placeholder_images[array_rand($placeholder_images)]; ?>
                        <img src="<?php echo $paths['base_path']; ?>img/thumbs/<?php echo $random_placeholder; ?>" 
                             alt="<?php echo htmlspecialchars($post['title']); ?>" class="post-thumb-img placeholder">
                    <?php endif; ?>
                </div>
                <div class="post-card-content">
                    <h3 class="post-card-title"><?php echo htmlspecialchars($post['title']); ?></h3>
                    <p class="post-card-excerpt"><?php echo htmlspecialchars(is_string($post['excerpt']) ? $post['excerpt'] : ''); ?></p>
                    <div class="post-card-meta">
                        <?php if (isset($post['author']) && $post['author'] && is_string($post['author'])): ?>
                            <span class="post-author">By <?php echo htmlspecialchars($post['author']); ?></span>
                        <?php endif; ?>
                        <?php if (isset($post['date']) && $post['date']): ?>
                            <span class="post-date"><?php echo is_string($post['date']) ? htmlspecialchars($post['date']) : ''; ?></span>
                        <?php endif; ?>
                    </div>
                    <?php if (!empty($post['tags'])): ?>
                        <div class="post-card-tags">
                            <?php foreach ($post['tags'] as $tag): ?>
                                <span class="tag"><?php echo htmlspecialchars($tag); ?></span>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
                </a>
            </div>
        <?php endforeach; ?>
    </div>
</div>
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>