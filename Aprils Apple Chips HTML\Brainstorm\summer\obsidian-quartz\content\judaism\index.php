<?php
// Auto-generated category index
// Category: judaism

// Load path helper and configuration with fallback
$pathPrefix = '../';
if (file_exists(__DIR__ . '/' . $pathPrefix . 'path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . 'path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . 'config.php';
} elseif (file_exists(__DIR__ . '/' . $pathPrefix . '../path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . '../path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . '../config.php';
} else {
    die('Could not find path-helper.php');
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'Judaism Index';
$meta_description = 'Judaism Index';
$meta_keywords = 'judaism, posts, A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$related_posts = [];

// Category posts data
$category_posts = array (
  0 => 
  array (
    'title' => 'Donkey Carrying Books -- Ask the Rabbi',
    'author' => NULL,
    'date' => NULL,
    'excerpt' => 'The verse suggests that scholars will willingly load themselves with books and learning. A person who has many books but has not digested their contents. Can indicate someone who is well-stocked with knowledge and handles it intelligently.',
    'url' => 'donkey-carrying-books.php',
    'tags' => 
    array (
      0 => 'jewish',
      1 => 'spiritual',
      2 => 'palestine',
      3 => 'library',
    ),
    'filename' => 'donkey-carrying-books',
    'thumbnail' => '../../img/donkey.jpg',
  ),
  1 => 
  array (
    'title' => 'Hanukkah\'s Light - A Story of Resistance - Then and Now',
    'author' => 'Daniel Maté',
    'date' => NULL,
    'excerpt' => 'Hanukkah is often framed as a celebration of Jewish survival: a miracle of oil, a triumph over assimilation, a victory for the "home team." But like all stories, its meaning shifts depending on who holds the pen. Dig deeper, and you’ll find a holiday that mirrors today’s fiercest moral struggles—one that challenges us to choose between tribal loyalty and justice for all.',
    'url' => 'hanukkahs-light.php',
    'tags' => 
    array (
      0 => 'jewish',
      1 => 'palestine',
    ),
    'filename' => 'hanukkahs-light',
    'thumbnail' => NULL,
  ),
  2 => 
  array (
    'title' => 'Hebrew wasn\'t spoken for 2,000 years. It\'s revival was with the help of Spanish and Arabic.',
    'author' => 'A. A. Chips',
    'date' => NULL,
    'excerpt' => 'Hebrew wasn\'t spoken for 2,000 years. Here’s how it was revived.',
    'url' => 'hebrew-wasnt-spoken.php',
    'tags' => 
    array (
      0 => 'jewish',
      1 => 'palestine',
      2 => 'repost',
      3 => 'library',
    ),
    'filename' => 'hebrew-wasnt-spoken',
    'thumbnail' => NULL,
  ),
  3 => 
  array (
    'title' => 'The Door That Only Swings One Way: Awakening to Injustice',
    'author' => NULL,
    'date' => '2024-04-07',
    'excerpt' => 'A reflection on the inevitability of awakening to injustice and the consequences of denial.',
    'url' => 'door-swings-one-way.php',
    'tags' => 
    array (
      0 => 'jewish',
      1 => 'palestine',
      2 => 'alienation',
    ),
    'filename' => 'door-swings-one-way',
    'thumbnail' => NULL,
  ),
  4 => 
  array (
    'title' => 'Kaddish for the Soul of Judaism',
    'author' => 'Amanda Gellender',
    'date' => '2024-02-01',
    'excerpt' => 'As I write this, the wheels of genocide are turning. As I write this, I am preparing for Shabbat. When I see Gaza, I see my own people languishing in concentration camps. I see a world that has turned its back on us, letting us be slaughtered en masse because we aren’t quite human enough. I am having a nightmare, can you wake me up?',
    'url' => 'kaddish.php',
    'tags' => 
    array (
      0 => 'jewish',
      1 => 'palestine',
    ),
    'filename' => 'kaddish',
    'thumbnail' => NULL,
  ),
  5 => 
  array (
    'title' => 'A New Mass Religious and Spiritual Movement in 2024',
    'author' => 'A. A. Chips',
    'date' => '2024-01-01',
    'excerpt' => 'Are We Witnessing the Birth of a New Collective Spirituality?',
    'url' => 'new-collective-spirituality.php',
    'tags' => 
    array (
      0 => 'palestine',
      1 => 'advocacy',
      2 => 'spiritual',
      3 => 'masscommunication',
      4 => 'climate',
    ),
    'filename' => 'new-collective-spirituality',
    'thumbnail' => NULL,
  ),
  6 => 
  array (
    'title' => 'A Vision of Survival as Spiritual
"Date:": April 12, 2024',
    'author' => 'A. A. Chips',
    'date' => NULL,
    'excerpt' => 'We are being forced into a spirituality that makes no distinction between praying and planting, between mourning and mobilizing—because the end of the world is already here, and so is the beginning of the next.',
    'url' => 'survival-as-spiritual.php',
    'tags' => 
    array (
      0 => 'palestine',
      1 => 'addiction',
      2 => 'climate',
      3 => 'advocacy',
    ),
    'filename' => 'survival-as-spiritual',
    'thumbnail' => NULL,
  ),
  7 => 
  array (
    'title' => 'The news is at once horrifying to take in and impossible to ignore — but we are in this together.',
    'author' => 'Molly Tolsky',
    'date' => '2023-10-10',
    'excerpt' => 'And maybe we will see that while we’re all coming from our own unique blend of backgrounds and views, there’s really so much we have in common, and the same goals — peace, safety, hope, understanding — that we want in the end.',
    'url' => 'jewish-friends-not-okay.php',
    'tags' => 
    array (
      0 => 'palestine',
      1 => 'jewish',
    ),
    'filename' => 'jewish-friends-not-okay',
    'thumbnail' => NULL,
  ),
  8 => 
  array (
    'title' => 'Nearly 90 years after the great visionary died, allow his wise words to uplift you - Kahlil Gibran',
    'author' => 'Katy Gillett - The National News',
    'date' => '2019-04-10',
    'excerpt' => 'Kahlil Gibran died on April 10, 1931 in New York. The Lebanese-American writer, poet and visual artist was just 48 years old. And yet, nearly 90 years later, he\'s remembered as one of the great visionaries of his time, a pivotal figure on the Arabic literature scene of the early 1900s, thanks to his romantic style and prose poetry.',
    'url' => 'kahlil-gibran-quotes.php',
    'tags' => 
    array (
      0 => 'palestine',
      1 => 'repost',
      2 => 'library',
      3 => 'music',
      4 => 'love-story',
    ),
    'filename' => 'kahlil-gibran-quotes',
    'thumbnail' => NULL,
  ),
  9 => 
  array (
    'title' => '14 Ladino Phrases Every Jew Should Know - HeyAlma',
    'author' => NULL,
    'date' => '2019-03-15',
    'excerpt' => '## 14 Ladino Phrases -HeyAlma Original by Frances Johnson March 15, 2019 https://www.heyalma.com/14-ladino-phrases-every-jew-should-know/ ### 1. “Gu...',
    'url' => 'ladino-phrases.php',
    'tags' => 
    array (
      0 => 'jewish',
      1 => 'spanish',
    ),
    'filename' => 'ladino-phrases',
    'thumbnail' => NULL,
  ),
  10 => 
  array (
    'title' => 'Liberation Seder Documentary',
    'author' => 'IfNotNow',
    'date' => '2016-04-26',
    'excerpt' => 'On April 26, 2016, over 500 young American Jews risked arrest in five cities during Passover to declare "Dayenu! - Enough!" against American Jewish support for the occupation.',
    'url' => 'liberation-seder.php',
    'tags' => 
    array (
      0 => 'jewish',
      1 => 'liberation',
      2 => 'seder',
      3 => 'passover',
      4 => 'occupation',
      5 => 'activism',
      6 => 'resistance',
      7 => 'documentary',
    ),
    'filename' => 'liberation-seder',
    'thumbnail' => NULL,
  ),
  11 => 
  array (
    'title' => 'The First Liturgy of the Drowned
"Date:": May 30, 2025',
    'author' => 'A. A. Chips',
    'date' => NULL,
    'excerpt' => '#palestine #spiritual #CompassionateCities ### The First Liturgy of the Drowned ![[bottleboat.jpg]] **2031. The California Delta.** The priest wore a ...',
    'url' => 'liturgy-of-the-drowned.php',
    'tags' => 
    array (
      0 => 'palestine',
      1 => 'spiritual',
      2 => 'advocacy',
      3 => 'writings',
    ),
    'filename' => 'liturgy-of-the-drowned',
    'thumbnail' => NULL,
  ),
  12 => 
  array (
    'title' => 'What Does It Mean to Be Semitic?  Semitic Semantics..',
    'author' => 'A. A. Chips',
    'date' => '2025-05-20',
    'excerpt' => 'Reclaiming Language, History, and Shared Humanity',
    'url' => 'semitic-semantics.php',
    'tags' => 
    array (
      0 => 'jewish',
      1 => 'palestine',
      2 => 'advocacy',
      3 => 'writings',
    ),
    'filename' => 'semitic-semantics',
    'thumbnail' => NULL,
  ),
  13 => 
  array (
    'title' => 'When You Lose a Parent to Hate and Disinformation',
    'author' => 'A. A. Chips',
    'date' => '2024-12-19',
    'excerpt' => 'Today’s letter comes from someone grappling with a deeply painful family divide. It’s a situation many can relate to, especially in a world where political and ideological differences can strain even the closest relationships. Here’s their story, followed by my thoughts and guidance.',
    'url' => 'parent-lost-disinformation-hate.php',
    'tags' => 
    array (
      0 => 'alienation',
      1 => 'journal',
    ),
    'filename' => 'parent-lost-disinformation-hate',
    'thumbnail' => NULL,
  ),
  14 => 
  array (
    'title' => 'For the Safety of Jews and Palestinians, Stop Weaponizing Antisemitism',
    'author' => 'Bernie Steinberg - Harvard Hillel Executive Director from 1993 to 2010',
    'date' => '2023-12-29',
    'excerpt' => 'Former Harvard Hillel Director Bernie Steinberg warns against the weaponization of antisemitism to silence criticism of Israel. In a Harvard Crimson op-ed, Steinberg, a long-time figure in Jewish campus life, calls the "manufacturing of an antisemitism scare" a "McCarthyist tactic" used to bully pro-Palestine organizers and those not showing "due deference." He defends Harvard President Claudine Gay against smear campaigns and emphasizes that demanding justice for',
    'url' => 'safety-of-jews-and-palestinians.php',
    'tags' => 
    array (
    ),
    'filename' => 'safety-of-jews-and-palestinians',
    'thumbnail' => NULL,
  ),
);

// Generate content
ob_start();
?>
<div class="category-index">
    <header class="category-header">
        <h1><?php echo htmlspecialchars($page_title); ?></h1>
    </header>

    <div class="category-content">
        <?php echo <<<'HTML'
<p>_For my Jewish cousins, my Arab siblings, and everyone caught in the crossfire of propaganda_</p>

<p>I grew up Jewish-American. I attended a Sunday school until the third grade. I slept through a lot of it. I was Bar-Mitz-Fah'd at 13. Our family was never religious, but celebrated some of the holidays. My Dad's side of the family is originally from Poland and Ukraine. I know nothing about what it is like in those places. I don't have a grasp on the languages of my ancestors (Polish? Ukrainian? Yiddish?). They were not from the Levant. They were not from Israel or Palestine. My family is Ashkenazi. Most of my ancestry on that side of my family came from the Pale, which is now part of Ukraine.</p>

<p>My parents separated when I was 17. It was long needed. The didn't really share anything in common. My mother came from French Canadian settlers that likely intermingled with the indigenous people of North America. That's a very mild way to say there was likely sexual violence and murder. I don't know. But I have met Native American people with the same surname who I consider family. My maternal grandfather was always good to me and his other grandchildren. We would do yearly visits to his home in Cape Cod. I miss him, but I don't really forgive him. Since a young age, he would send me these chain emails with racist vitriol towards Immigrants and non-English speakers. He would follow political bandwagons about border security and enforcing English-only speaking policies. When I spent time around that part of my family as a kid, I was very cruel towards my peers at school. It's like I was indoctrinated towards being a cruel racist bully around them. I didn't like that. I live with that shame to this day, and knew as a kid that this didn't match my values as a person.</p>

<p>However my entire life, especially growing up, I have always been around Spanish. I've learned Spanish for most of my adult and adolescent life. I have been to México, and am way more fond and competent of Spanish-speaking cultures. It wasn't until I was in my late twenties that I learned that there are Spanish speaking Jews. I was never taught this by anybody growing up.</p>

<p>When my life fell apart, when I blocked my mother, and exiled myself to a different state to be homeless and live out of my car, I found myself connecting deeply to the story of the Edict of Expulsión in what we know as Spain in 1492. I found comfort and solace in the bits of Sephardic and Ladino (Judaeo-Spanish) content I could find on the internet. I would find myself seeking refuge in Christian faith communities being on new ground in the American South. This is also very much part of the American Sephardic experience, where Sephardics who fled to the Americas pretended to be Christian while hiding from the Spanish Inquisition, an institution of persecution that lasted for over 300 years.</p>

<p>I learned about Al-Andalus, the Golden Age of Jewish and Islamic culture in Spain. I learned that Jewish people and Muslims have lived and coexisted with each other for a very long time as friends and neighbors. This matched my lived experience growing up around Muslims in a large metropolitan area outside of Washington D.C. as well.</p>

<p>I'm proudly Sephardic Jewish and support a free Palestine. There is nothing Jewish about Israel's actions or history.</p>

<p>I wanted to share my Israelism story because I know there are many American Jews out there questioning their relationship with who they are and what Israel is doing. I hope that by sharing my story, it can help others find their way. I also want to share because there are many people who have suffered unthinkable loss at the hands of the United States and Israel's militarism and barbarity. I want to separate myself from that by sharing this, and a vault of resources I have found helpful and redemptive in re-educating myself.</p>

<p>Not in my name.</p>

<ul>
<ul><li>
<p><a href="safety-of-jews-and-palestinians.php" class="internal-link">For the Safety of Jews and Palestinians, Stop Weaponizing Antisemitism</a></p>
<p><p>Former Harvard Hillel Director Bernie Steinberg warns against the weaponization of antisemitism to silence criticism of Israel. In a Harvard Crimson op-ed, Steinberg, a long-time figure in Jewish campus life, calls the "manufacturing of an antisemitism scare" a "McCarthyist tactic" used to bully pro-Palestine organizers and those not showing "due deference." He defends Harvard President Claudine Gay against smear campaigns and emphasizes that demanding justice for Palestinians is not antisemitic.</p></p>
<p></li></p>
<p><li></p>
<p><a href="hanukkahs-light.php" class="internal-link">Hanukkah's Light - A Story of Resistance - Then and Now</a></p>
<p><p>Hanukkah is often framed as a celebration of Jewish survival: a miracle of oil, a triumph over assimilation, a victory for the "home team." But like all stories, its meaning shifts depending on who holds the pen. Dig deeper, and you’ll find a holiday that mirrors today’s fiercest moral struggles—one that challenges us to choose between tribal loyalty and justice for all.</p></p>
<p></li></p>
<p><li></p>
<p><a href="semitic-semantics.php" class="internal-link">What Does It Mean to Be Semitic? Reclaiming Language, History, and Shared Humanity</a></p>
<p><p>For my Jewish cousins, my Arab siblings, and everyone caught in the crossfire of propaganda. The word _Semitic_ refers to languages, not ethnicities or religions. It describes a linguistic family that includes:</p></p>
<p></li></p>
<p><li></p>
<p><a href="kaddish.php" class="internal-link">Kaddish for the Soul of Judaism</a></p>
<p><p>There is a great article written by Amanda Gellender, an American Jew, titled <a href="https://agelender.medium.com/kaddish-for-the-soul-of-judaism-genocide-in-palestine-1248d7ff2611" class="external-link">Kaddish for the Soul of Judaism</a>. I have it in print and have read it a dozen times for comfort and solace. I recommend every word.</p></p>
<p></li></p>
<p><li></p>
<p><a href="survival-as-spiritual.php" class="internal-link">Survival as Spiritual</a></p>
<p><p>I've been asking what happens when millions lay witness over live streams of genocide, what the long term impact of that will be. If we survive as a species and as advanced life on Earth, I think it will be from a resurgence of a new way of thinking of religion and faith. <br><br>"We are being forced into a spirituality that makes no distinction between praying and planting, between mourning and mobilizing—because the end of the world is already here, and so is the beginning of the next."</p></p>
<p></li></p>
<p><li></p>
<p><a href="liberation-seder.php">Liberation Seder Documentary - 2016 - IfNotNow</a></p>
<p><p>On April 26, 2016, over 500 young American Jews risked arrest in five cities during Passover to declare "Dayenu! - Enough!" against American Jewish support for the occupation.</p></p>
<p></li></p>
<p><li></p>
<p><a href="links-for-palestine.php" class="internal-link">Links for Palestine</a></p>
<p><p>A collection of resources for learning more about Palestine and Palestinian people.</p></p>
<p></li></p>
<p><li></p>
<p><a href="palestine-memes-and-art.php" class="internal-link">Palestine Memes and Art</a></p>
<p><p>A collection of memes and art for Palestine.</p></p>
<p></li></p>
<p><li></p>
<p><a href="donkey-carrying-books.php">Donkey Carrying Books -- Ask the Rabbi</a></p>
<p><p>After my brain injury, I have had trouble reading like I used to when I was younger. Someone threw this phrase at me endearingly, and I had to look it up. The verse suggests that scholars will willingly load themselves with books and learning. A person who has many books but has not digested their contents. Can indicate someone who is well-stocked with knowledge and handles it intelligently.</p></p>
<p></li></p>
<p><li></p>
<p><a href="new-collective-spirituality.php" class="internal-link">New Collective Spirituality</a></p>
<p><p>The internet’s horror-show has stripped away illusions. If humanity survives this era, it will be because we forged a spirituality of _action_—one that honors the martyrs of Gaza, the Congo, and every silenced crisis by dismantling the systems that created them.</p></p>
<p></li></p>
<p><li></p>
<p><a href="hebrew-wasnt-spoken.php">Hebrew wasn't spoken for 2,000 years. It's revival was with the help of Spanish and Arabic.</a></p>
<p><p>The story of Hebrew's revival is a testament to the power of cultural exchange and the importance of historical perspective. As we reflect on this journey, it is essential to recognize the role that Arabic played in bringing Hebrew back to life. Eliezer Ben-Yehuda's vision of a society where Hebrew and Arabic coexist in harmony is a goal worth striving for. By embracing the historical connections between these languages, we can foster a more inclusive and compassionate communication, enriching both cultures and building a better future for all.</p></p>
<p></li></p>
<p><li></p>
<p><a href="kahlil-gibran-quotes.php">Kahlil Gibran Quotes</a></p>
<p><p>A collection of quotes from Kahlil Gibran, a Lebanese-American poet and writer.</p></p>
<p></li></p>
<p><li></p>
<p><a href="nothing-has-remained-ahmed-miqdad.php">Nothing has remained - Ahmed Miqdad</a></p>
<p><p>A powerful poem by Ahmed Miqdad, a Palestinian poet and activist.</p></p>
<p></li></p>
<p><li></p>
<p><a href="door-swings-one-way.php">The Door that Only Swings One Way..</a></p>
<p><p>A reflection on the inevitability of awakening to injustice and the consequences of denial.</p></p>
<p></li></p>
<p><li></p>
<p><a href="parent-lost-disinformation-hate.php">When You Lose a Parent to Hate and Disinformation</a></p>
<p><p>Today’s letter comes from someone grappling with a deeply painful family divide. It’s a situation many can relate to, especially in a world where political and ideological differences can strain even the closest relationships. Here’s their story, followed by my thoughts and guidance.</p></p>
<p></li></ul></p>
<p></ul></p>


HTML;
        ?>
    </div>

    <div class="post-grid">
        <?php foreach ($category_posts as $post): ?>
            <div class="post-card">
                <a href="<?php echo htmlspecialchars($post['url']); ?>" class="post-card-link">
                <div class="post-card-thumbnail">
                    <?php if (isset($post['thumbnail']) && $post['thumbnail']): ?>
                        <?php if (preg_match('/^https?:\/\//', $post['thumbnail'])): ?>
                            <img src="<?php echo htmlspecialchars($post['thumbnail']); ?>" 
                                 alt="<?php echo htmlspecialchars($post['title']); ?>" class="post-thumb-img">
                        <?php else: ?>
                            <img src="<?php echo $paths['base_path']; ?>img/thumbs/<?php echo htmlspecialchars($post['thumbnail']); ?>" 
                                 alt="<?php echo htmlspecialchars($post['title']); ?>" class="post-thumb-img">
                        <?php endif; ?>
                    <?php else: ?>
                        <?php $placeholder_images = ['placeholder1.jpg', 'placeholder2.jpg', 'placeholder3.jpg', 'placeholder4.jpg']; ?>
                        <?php $random_placeholder = $placeholder_images[array_rand($placeholder_images)]; ?>
                        <img src="<?php echo $paths['base_path']; ?>img/thumbs/<?php echo $random_placeholder; ?>" 
                             alt="<?php echo htmlspecialchars($post['title']); ?>" class="post-thumb-img placeholder">
                    <?php endif; ?>
                </div>
                <div class="post-card-content">
                    <h3 class="post-card-title"><?php echo htmlspecialchars($post['title']); ?></h3>
                    <p class="post-card-excerpt"><?php echo htmlspecialchars(is_string($post['excerpt']) ? $post['excerpt'] : ''); ?></p>
                    <div class="post-card-meta">
                        <?php if (isset($post['author']) && $post['author'] && is_string($post['author'])): ?>
                            <span class="post-author">By <?php echo htmlspecialchars($post['author']); ?></span>
                        <?php endif; ?>
                        <?php if (isset($post['date']) && $post['date']): ?>
                            <span class="post-date"><?php echo is_string($post['date']) ? htmlspecialchars($post['date']) : ''; ?></span>
                        <?php endif; ?>
                    </div>
                    <?php if (!empty($post['tags'])): ?>
                        <div class="post-card-tags">
                            <?php foreach ($post['tags'] as $tag): ?>
                                <span class="tag"><?php echo htmlspecialchars($tag); ?></span>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
                </a>
            </div>
        <?php endforeach; ?>
    </div>
</div>
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>