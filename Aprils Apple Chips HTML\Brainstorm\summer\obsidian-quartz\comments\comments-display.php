<?php
/**
 * Comments Display Component
 * For A. A. Chips' Obsidian-Quartz Comments System
 */

session_start();
require_once __DIR__ . '/database.php';
require_once __DIR__ . '/google-auth.php';

function renderCommentsSection($postSlug) {
    $db = CommentDatabase::getInstance();
    $config = $db->getConfig();
    $currentUser = GoogleAuth::getCurrentUser();
    $commentCount = $db->getCommentCount($postSlug);
    
    ob_start();
    ?>
    <section class="comments-section" id="comments">
        <div class="comments-header">
            <h3>Comments (<span id="comment-count"><?php echo $commentCount; ?></span>)</h3>
        </div>

        <?php if ($currentUser): ?>
            <div class="comment-form-container">
                <div class="user-info">
                    <img src="<?php echo htmlspecialchars($currentUser['picture_url'] ?? ''); ?>" 
                         alt="<?php echo htmlspecialchars($currentUser['name']); ?>" 
                         class="user-avatar">
                    <span class="user-name"><?php echo htmlspecialchars($currentUser['name']); ?></span>
                    <a href="comments/google-auth.php?logout=1&redirect=<?php echo urlencode($_SERVER['REQUEST_URI']); ?>" 
                       class="logout-link">Logout</a>
                </div>
                
                <form id="comment-form" class="comment-form">
                    <input type="hidden" name="post_slug" value="<?php echo htmlspecialchars($postSlug); ?>">
                    <input type="hidden" name="parent_id" value="">
                    <input type="hidden" name="<?php echo $config['spam']['honeypot_field']; ?>" value="" style="display: none;">
                    
                    <div class="form-group">
                        <label for="comment-content" class="sr-only">Your comment</label>
                        <textarea id="comment-content" 
                                  name="content" 
                                  placeholder="Share your thoughts..." 
                                  rows="4" 
                                  maxlength="<?php echo $config['comments']['max_comment_length']; ?>"
                                  required></textarea>
                        <div class="character-count">
                            <span id="char-count">0</span> / <?php echo $config['comments']['max_comment_length']; ?>
                        </div>
                    </div>
                    
                    <div class="form-actions">
                        <button type="button" id="cancel-reply" class="btn btn-secondary" style="display: none;">Cancel Reply</button>
                        <button type="submit" class="btn btn-primary">Post Comment</button>
                    </div>
                </form>
            </div>
        <?php else: ?>
            <div class="login-prompt">
                <p>Please sign in with Google to leave a comment.</p>
                <a href="comments/google-auth.php?redirect=<?php echo urlencode($_SERVER['REQUEST_URI']); ?>" 
                   class="btn btn-google">
                    <svg width="18" height="18" viewBox="0 0 24 24">
                        <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                        <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                        <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                        <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                    </svg>
                    Sign in with Google
                </a>
            </div>
        <?php endif; ?>

        <div id="comments-container" class="comments-container">
            <div id="comments-loading" class="loading" style="display: none;">
                Loading comments...
            </div>
            <div id="comments-list" class="comments-list">
                <!-- Comments will be loaded here via JavaScript -->
            </div>
            <div id="load-more-container" style="display: none;">
                <button id="load-more-comments" class="btn btn-secondary">Load More Comments</button>
            </div>
        </div>
    </section>

    <script>
        // Initialize comments system
        document.addEventListener('DOMContentLoaded', function() {
            const commentsSystem = new CommentsSystem('<?php echo htmlspecialchars($postSlug); ?>');
            commentsSystem.init();
        });
    </script>
    <?php
    return ob_get_clean();
}

function getPostSlugFromUrl() {
    $path = $_SERVER['REQUEST_URI'];
    $path = parse_url($path, PHP_URL_PATH);
    $path = trim($path, '/');
    
    // Remove .php extension if present
    if (substr($path, -4) === '.php') {
        $path = substr($path, 0, -4);
    }
    
    // Remove content/ prefix if present
    if (strpos($path, 'content/') === 0) {
        $path = substr($path, 8);
    }
    
    return $path ?: 'index';
}

// Auto-render comments if this file is included
if (!function_exists('renderCommentsSection_called')) {
    function renderCommentsSection_called() {
        return true;
    }
    
    // Only auto-render if we're in a post context (not in the comments directory)
    if (strpos(__FILE__, 'comments/') === false) {
        $postSlug = getPostSlugFromUrl();
        echo renderCommentsSection($postSlug);
    }
}
?>
