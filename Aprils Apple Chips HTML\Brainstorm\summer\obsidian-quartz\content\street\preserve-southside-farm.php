<?php
// Auto-generated blog post
// Source: preserve-southside-farm.md

// Load path helper and configuration with fallback
$pathPrefix = '../';
if (file_exists(__DIR__ . '/' . $pathPrefix . 'path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . 'path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . 'config.php';
} elseif (file_exists(__DIR__ . '/' . $pathPrefix . '../path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . '../path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . '../config.php';
} else {
    die('Could not find path-helper.php');
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'Preserve the Southside Urban Farm --- Securing a Sustainable Future';
$meta_description = 'As an alumni of the Green Opportunities Kitchen Ready Training program at the Southside Kitchen, I cannot stress enough the profound impact this urban farm has had on my life.';
$meta_keywords = 'climate, advocacy, applechipkitchen, A. A. Chips, blog, personal stories, advocacy, homelessness, family alienation, rebuilding life';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = $paths['base_path'] . 'img/thumbs/' . '../../img/greenopportunities.jpg';
$related_posts = [
    ['title' => 'Alienation', 'url' => PathHelper::getCategoryIndexPath('alienation'), 'excerpt' => 'Browse all Alienation posts'],
    ['title' => 'Climate', 'url' => PathHelper::getCategoryIndexPath('climate'), 'excerpt' => 'Browse all Climate posts'],
    ['title' => 'Humor', 'url' => PathHelper::getCategoryIndexPath('humor'), 'excerpt' => 'Browse all Humor posts'],
    ['title' => 'Inspiration', 'url' => PathHelper::getCategoryIndexPath('inspiration'), 'excerpt' => 'Browse all Inspiration posts'],
    ['title' => 'Journal', 'url' => PathHelper::getCategoryIndexPath('journal'), 'excerpt' => 'Browse all Journal posts'],
    ['title' => 'Judaism', 'url' => PathHelper::getCategoryIndexPath('judaism'), 'excerpt' => 'Browse all Judaism posts'],
    ['title' => 'Kitchen', 'url' => PathHelper::getCategoryIndexPath('kitchen'), 'excerpt' => 'Browse all Kitchen posts'],
    ['title' => 'Ptsd Myth', 'url' => PathHelper::getCategoryIndexPath('ptsd-myth'), 'excerpt' => 'Browse all Ptsd Myth posts'],
    ['title' => 'Street', 'url' => PathHelper::getCategoryIndexPath('street'), 'excerpt' => 'Browse all Street posts'],
    ['title' => 'Writings', 'url' => PathHelper::getCategoryIndexPath('writings'), 'excerpt' => 'Browse all Writings posts'],
];

$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'Preserve the Southside Urban Farm --- Securing a Sustainable Future',
  'author' => 'A. A. Chips',
  'source' => 'https://aachips.co/blog/preserve-southside-farm/',
  'tags' => 
  array (
    0 => 'climate',
    1 => 'advocacy',
    2 => 'applechipkitchen',
  ),
  'excerpt' => 'As an alumni of the Green Opportunities Kitchen Ready Training program at the Southside Kitchen, I cannot stress enough the profound impact this urban farm has had on my life.',
  'date' => '2024-04-15',
  'thumbnail' => '../../img/greenopportunities.jpg',
  'source_file' => 'content\\street\\preserve-southside-farm.md',
);

// Raw content
$post_content = '<p>My plea to members of the Asheville Housing Authority: Do not pass Resolution No. 2024-11. Please vote to preserve the Southside Farm. </p>

<p>As an alumni of the Green Opportunities Kitchen Ready Training program at the Southside Kitchen, I cannot stress enough the profound impact this urban farm has had on my life and the broader community of Asheville. </p>

<p>For nearly a decade, I had mentored and trained essential kitchen functions like recycling and composting in different food settings. I applied these skills as a kitchen-ready culinary student there in 2019. I had been recruited as an at-risk young adult who had been homeless in the area for around a year and a half. That experience has led me to become a seasonal small business owner who makes apple chips and a local educator on food security.</p>

<p>I worked 200 feet away from the Southside Farm. The proximity to a garden made composting food scraps in a restaurant kitchen much more accessible, particularly to students training to work in the local food economy. Not every restaurant worker, chef, and manager knows how to recycle and compost. Doing so is a helpful sanitation practice. It is accessible and scalable with the right partnerships. And it continues a process where our viable food scraps can provide nutrition back into the food system and provide us with food security and resilience for tomorrow.</p>

<p>When I think of a playground, I imagine a place with rich hearty soil, and lots of worms. I imagine young kids helping out in the garden and learning about botany at an early age. I imagine the joy on their faces bringing fresh grown produce home that they helped grow that season.</p>

<p>The Southside Urban Farm provides so much educational, nutritional, and neighborly value to Asheville and Southside. Losing Southside Farm would have disastrous economic consequences for the local restaurant economy and community as a whole.  For years, Asheville has been a model and shooting star of food recovery projects that work towards a circular food system. Urban farms and community gardens provide so much more long term economic value, and we need more, not less of these spaces in the city proper.</p>

<p>The loss of the Southside Urban Farm would result in several negative consequences for Asheville:</p>

<ul><li>Reduced Food Security: Eliminating the farm would limit access to fresh, locally produced foods, potentially increasing dependence on imports and decreasing access to nutritious options.</li>
<p><li>Loss of Educational Opportunities: The farm teaches various age groups about horticulture, sustainable agriculture, and environmental stewardship. Removing it would limit hands-on learning experiences and skill development.</li></p>
<p><li>Environmental Degradation: Industrialization of the site may lead to increased pollution, water usage, and energy consumption, harming the surrounding ecosystem.</li></ul></p>

<p>My plea to the Housing Authority: Safeguard the Southside Urban Farm to continue existing in its current form. This is an essential community asset.</p>

<h3>Vote NO on  Resolution No. 2024-11.</h3>';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta"><?php echo implode('<span class="meta-separator"> • </span>', $metadata); ?></div>
    <?php endif; ?>
    
    <?php if (isset($post_data['excerpt'])): ?>
        <div class="post-excerpt">
            <p><em><?php echo htmlspecialchars($post_data['excerpt']); ?></em></p>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>